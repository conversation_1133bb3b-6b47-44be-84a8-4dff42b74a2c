{"name": "troika-three-text", "version": "0.52.4", "description": "SDF-based text rendering for Three.js", "author": "<PERSON> <<EMAIL>>", "repository": {"type": "git", "url": "https://github.com/protectwise/troika.git", "directory": "packages/troika-three-text"}, "license": "MIT", "sideEffects": false, "main": "dist/troika-three-text.umd.js", "module": "dist/troika-three-text.esm.js", "module:src": "src/index.js", "dependencies": {"bidi-js": "^1.0.2", "troika-three-utils": "^0.52.4", "troika-worker-utils": "^0.52.0", "webgl-sdf-generator": "1.1.1"}, "peerDependencies": {"three": ">=0.125.0"}, "devDependencies": {"@lojjic/typr": "0.3.8-lojjic1", "@unicode-font-resolver/client": "1.0.2", "fflate": "^0.7.0", "node-fetch": "^2.6.0"}, "scripts": {"build-typr": "rollup -c rollup.config.build-typr.js", "build-woff2otf": "rollup -c rollup.config.build-woff2otf.js", "build-unicode-font-resolver": "rollup -c rollup.config.build-unicode-font-resolver.js"}, "gitHead": "728a1780127df0c2509e967b03f17ddc46bbf5d2"}