"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),r=require("react"),t=require("@react-three/fiber");function n(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function u(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var n=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,n.get?n:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}var c=n(e),a=u(r);const s=a.forwardRef((({children:e,hysteresis:r=0,distances:n,...u},s)=>{const l=a.useRef(null);return a.useImperativeHandle(s,(()=>l.current),[]),a.useLayoutEffect((()=>{const{current:e}=l;e.levels.length=0,e.children.forEach(((t,u)=>e.levels.push({object:t,hysteresis:r,distance:n[u]})))})),t.useFrame((e=>{var r;return null==(r=l.current)?void 0:r.update(e.camera)})),a.createElement("lOD",c.default({ref:l},u),e)}));exports.Detailed=s;
