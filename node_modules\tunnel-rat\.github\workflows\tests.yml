name: Tests

on:
  push:
    branches:
      - 'main'
  pull_request: {}

jobs:
  build:
    name: Build and Run Tests
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repo
        uses: actions/checkout@v2

      - name: Set up Node
        uses: actions/setup-node@v1
        with:
          node-version: 16

      - name: Set up Yarn cache
        uses: c-hive/gha-yarn-cache@v2

      - name: Install dependencies
        run: yarn install

      - name: Build packages
        run: yarn build

      - name: Test packages
        run: yarn test
