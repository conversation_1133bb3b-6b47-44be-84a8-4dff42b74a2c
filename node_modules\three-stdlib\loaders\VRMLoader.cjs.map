{"version": 3, "file": "VRMLoader.cjs", "sources": ["../../src/loaders/VRMLoader.js"], "sourcesContent": ["import { Loader } from 'three'\nimport { GLTFLoader } from '../loaders/GLTFLoader.js'\n\n// VRM Specification: https://dwango.github.io/vrm/vrm_spec/\n//\n// VRM is based on glTF 2.0 and VRM extension is defined\n// in top-level json.extensions.VRM\n\nclass VRMLoader extends Loader {\n  constructor(manager) {\n    super(manager)\n    this.gltfLoader = new GLTFLoader(manager)\n  }\n\n  load(url, onLoad, onProgress, onError) {\n    const scope = this\n\n    this.gltfLoader.load(\n      url,\n      function (gltf) {\n        try {\n          scope.parse(gltf, onLoad)\n        } catch (e) {\n          if (onError) {\n            onError(e)\n          } else {\n            console.error(e)\n          }\n\n          scope.manager.itemError(url)\n        }\n      },\n      onProgress,\n      onError,\n    )\n  }\n\n  setDRACOLoader(dracoLoader) {\n    this.gltfLoader.setDRACOLoader(dracoLoader)\n    return this\n  }\n\n  parse(gltf, onLoad) {\n    // const gltfParser = gltf.parser;\n    // const gltfExtensions = gltf.userData.gltfExtensions || {};\n    // const vrmExtension = gltfExtensions.VRM || {};\n\n    // handle VRM Extension here\n\n    onLoad(gltf)\n  }\n}\n\nexport { VRMLoader }\n"], "names": ["Loader", "GLTFLoader"], "mappings": ";;;;AAQA,MAAM,kBAAkBA,MAAAA,OAAO;AAAA,EAC7B,YAAY,SAAS;AACnB,UAAM,OAAO;AACb,SAAK,aAAa,IAAIC,WAAU,WAAC,OAAO;AAAA,EACzC;AAAA,EAED,KAAK,KAAK,QAAQ,YAAY,SAAS;AACrC,UAAM,QAAQ;AAEd,SAAK,WAAW;AAAA,MACd;AAAA,MACA,SAAU,MAAM;AACd,YAAI;AACF,gBAAM,MAAM,MAAM,MAAM;AAAA,QACzB,SAAQ,GAAP;AACA,cAAI,SAAS;AACX,oBAAQ,CAAC;AAAA,UACrB,OAAiB;AACL,oBAAQ,MAAM,CAAC;AAAA,UAChB;AAED,gBAAM,QAAQ,UAAU,GAAG;AAAA,QAC5B;AAAA,MACF;AAAA,MACD;AAAA,MACA;AAAA,IACD;AAAA,EACF;AAAA,EAED,eAAe,aAAa;AAC1B,SAAK,WAAW,eAAe,WAAW;AAC1C,WAAO;AAAA,EACR;AAAA,EAED,MAAM,MAAM,QAAQ;AAOlB,WAAO,IAAI;AAAA,EACZ;AACH;;"}